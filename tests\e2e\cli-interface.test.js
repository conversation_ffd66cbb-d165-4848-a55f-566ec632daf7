/**
 * End-to-end tests for CLI interface
 */

import { test, describe, beforeEach, afterEach } from 'node:test';
import assert from 'node:assert';
import { spawn } from 'child_process';
import { existsSync, rmSync, mkdirSync } from 'fs';
import { join } from 'path';
import { homedir } from 'os';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

describe('CLI Interface E2E Tests', () => {
  let testDir;
  const cliPath = join(__dirname, '../../src/index.js');

  beforeEach(() => {
    // Create test environment
    testDir = join(homedir(), '.arien-ai-test-e2e');
    if (existsSync(testDir)) {
      rmSync(testDir, { recursive: true, force: true });
    }
    mkdirSync(testDir, { recursive: true });
  });

  afterEach(() => {
    if (existsSync(testDir)) {
      rmSync(testDir, { recursive: true, force: true });
    }
  });

  function runCLI(args = [], options = {}) {
    return new Promise((resolve, reject) => {
      const child = spawn('node', [cliPath, ...args], {
        stdio: 'pipe',
        env: {
          ...process.env,
          ARIEN_CONFIG_DIR: testDir,
          ...options.env
        },
        timeout: options.timeout || 10000
      });

      let stdout = '';
      let stderr = '';

      child.stdout.on('data', (data) => {
        stdout += data.toString();
      });

      child.stderr.on('data', (data) => {
        stderr += data.toString();
      });

      child.on('close', (code) => {
        resolve({
          code,
          stdout,
          stderr
        });
      });

      child.on('error', (error) => {
        reject(error);
      });

      // Send input if provided
      if (options.input) {
        child.stdin.write(options.input);
        child.stdin.end();
      }
    });
  }

  test('should show help when no arguments provided', async () => {
    const result = await runCLI(['--help']);
    
    assert.strictEqual(result.code, 0);
    assert.ok(result.stdout.includes('Arien-AI'));
    assert.ok(result.stdout.includes('Usage') || result.stdout.includes('Commands'));
  });

  test('should show version information', async () => {
    const result = await runCLI(['--version']);
    
    assert.strictEqual(result.code, 0);
    assert.ok(result.stdout.match(/\d+\.\d+\.\d+/)); // Version pattern
  });

  test('should show status command', async () => {
    const result = await runCLI(['status']);
    
    // Should not fail even if providers are not configured
    assert.ok(result.code === 0 || result.code === 1);
    assert.ok(result.stdout.includes('Status') || result.stderr.includes('Error'));
  });

  test('should handle config command', async () => {
    const result = await runCLI(['config']);
    
    // Should show current configuration or initialization prompt
    assert.ok(result.code === 0 || result.code === 1);
    assert.ok(result.stdout.length > 0 || result.stderr.length > 0);
  });

  test('should handle config initialization', async () => {
    const result = await runCLI(['config', '--init'], {
      input: '\n\n\n\n\n\n', // Accept defaults for all prompts
      timeout: 15000
    });
    
    // May succeed or fail depending on environment
    assert.ok(typeof result.code === 'number');
  });

  test('should handle execute command with simple input', async () => {
    const result = await runCLI(['execute', 'help']);
    
    // Should attempt to process the command
    assert.ok(typeof result.code === 'number');
    assert.ok(result.stdout.length > 0 || result.stderr.length > 0);
  });

  test('should handle invalid command gracefully', async () => {
    const result = await runCLI(['invalid-command']);
    
    assert.notStrictEqual(result.code, 0);
    assert.ok(result.stderr.includes('Unknown') || result.stderr.includes('error'));
  });

  test('should handle execute with verbose flag', async () => {
    const result = await runCLI(['execute', 'status', '--verbose']);
    
    assert.ok(typeof result.code === 'number');
    // Verbose mode should produce output
    assert.ok(result.stdout.length > 0 || result.stderr.length > 0);
  });

  test('should handle execute with no-retry flag', async () => {
    const result = await runCLI(['execute', 'test', '--no-retry']);
    
    assert.ok(typeof result.code === 'number');
    // Should attempt execution without retry
  });

  test('should handle SIGINT gracefully', async () => {
    const child = spawn('node', [cliPath, 'interactive'], {
      stdio: 'pipe',
      env: {
        ...process.env,
        ARIEN_CONFIG_DIR: testDir
      }
    });

    let stdout = '';
    child.stdout.on('data', (data) => {
      stdout += data.toString();
    });

    // Wait a bit then send SIGINT
    setTimeout(() => {
      child.kill('SIGINT');
    }, 1000);

    const result = await new Promise((resolve) => {
      child.on('close', (code, signal) => {
        resolve({ code, signal, stdout });
      });
    });

    // Should handle graceful shutdown
    assert.ok(result.signal === 'SIGINT' || result.code !== null);
  });

  test('should handle environment variables', async () => {
    const result = await runCLI(['status'], {
      env: {
        DEEPSEEK_API_KEY: 'test-key',
        OLLAMA_BASE_URL: 'http://localhost:11434'
      }
    });
    
    // Should recognize environment variables
    assert.ok(typeof result.code === 'number');
  });

  test('should handle missing dependencies gracefully', async () => {
    // Test with minimal environment
    const result = await runCLI(['status'], {
      env: {
        PATH: '/usr/bin:/bin' // Minimal PATH
      }
    });
    
    // Should not crash even with missing optional dependencies
    assert.ok(typeof result.code === 'number');
  });

  test('should handle config reset', async () => {
    const result = await runCLI(['config', '--reset']);
    
    // Should reset configuration
    assert.ok(typeof result.code === 'number');
  });

  test('should handle long-running commands with timeout', async () => {
    const result = await runCLI(['execute', 'sleep 1'], {
      timeout: 5000
    });
    
    // Should either complete or timeout gracefully
    assert.ok(typeof result.code === 'number');
  });

  test('should handle special characters in input', async () => {
    const specialInput = 'echo "Hello & World | Test > Output"';
    const result = await runCLI(['execute', specialInput]);
    
    // Should handle special characters safely
    assert.ok(typeof result.code === 'number');
  });

  test('should handle unicode input', async () => {
    const unicodeInput = 'echo "Hello 世界 🌍"';
    const result = await runCLI(['execute', unicodeInput]);
    
    // Should handle unicode characters
    assert.ok(typeof result.code === 'number');
  });

  test('should handle empty execute command', async () => {
    const result = await runCLI(['execute', '']);
    
    // Should reject empty commands
    assert.notStrictEqual(result.code, 0);
  });

  test('should handle multiple rapid commands', async () => {
    const promises = [
      runCLI(['status']),
      runCLI(['status']),
      runCLI(['status'])
    ];
    
    const results = await Promise.allSettled(promises);
    
    // All commands should complete
    assert.strictEqual(results.length, 3);
    results.forEach(result => {
      assert.ok(result.status === 'fulfilled' || result.status === 'rejected');
    });
  });

  test('should handle config file corruption gracefully', async () => {
    // Create corrupted config file
    const { writeFileSync } = await import('fs');
    const configFile = join(testDir, 'config.json');
    writeFileSync(configFile, '{ invalid json }');

    const result = await runCLI(['status']);

    // Should handle corrupted config gracefully
    assert.ok(typeof result.code === 'number');
  });
});
